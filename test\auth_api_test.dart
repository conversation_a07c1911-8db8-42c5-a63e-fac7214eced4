import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:online_medicine/APIs/auth_api_service.dart';
import 'package:online_medicine/models/auth_models.dart';

void main() {
  group('AuthApiService Registration Tests', () {
    test('register method creates multipart request correctly', () async {
      // Create a test registration request
      final request = RegistrationRequest(
        fullName: 'Test User',
        phoneNumber: '01234567890',
        pharmacyName: 'Test Pharmacy',
        district: 'Dhaka',
        policeStation: 'Dhanmondi',
        pharmacyFullAddress: '123 Test Street, Dhaka',
        email: '<EMAIL>',
        password: 'testpassword',
        confirmPassword: 'testpassword',
        nidImagePath: null, // No image for this test
      );

      // This test verifies that the method can be called without throwing errors
      // In a real test environment, you would mock the HTTP client
      try {
        // Note: This will fail with network error since we don't have a real server
        // but it will test that our multipart request is properly formed
        await AuthApiService.register(request);
      } catch (e) {
        // Expected to fail with network error, but should not fail with compilation error
        expect(e, isA<Exception>());
      }
    });

    test('registration request model has correct fields', () {
      final request = RegistrationRequest(
        fullName: 'Test User',
        phoneNumber: '01234567890',
        pharmacyName: 'Test Pharmacy',
        district: 'Dhaka',
        policeStation: 'Dhanmondi',
        pharmacyFullAddress: '123 Test Street, Dhaka',
        email: '<EMAIL>',
        password: 'testpassword',
        confirmPassword: 'testpassword',
        nidImagePath: '/path/to/nid/image.jpg',
      );

      expect(request.fullName, equals('Test User'));
      expect(request.phoneNumber, equals('01234567890'));
      expect(request.pharmacyName, equals('Test Pharmacy'));
      expect(request.district, equals('Dhaka'));
      expect(request.policeStation, equals('Dhanmondi'));
      expect(request.pharmacyFullAddress, equals('123 Test Street, Dhaka'));
      expect(request.email, equals('<EMAIL>'));
      expect(request.password, equals('testpassword'));
      expect(request.confirmPassword, equals('testpassword'));
      expect(request.nidImagePath, equals('/path/to/nid/image.jpg'));
    });

    test('registration request handles null nidImagePath', () {
      final request = RegistrationRequest(
        fullName: 'Test User',
        phoneNumber: '01234567890',
        pharmacyName: 'Test Pharmacy',
        district: 'Dhaka',
        policeStation: 'Dhanmondi',
        pharmacyFullAddress: '123 Test Street, Dhaka',
        email: '<EMAIL>',
        password: 'testpassword',
        confirmPassword: 'testpassword',
        nidImagePath: null,
      );

      expect(request.nidImagePath, isNull);
    });
  });
}
